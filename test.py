#!/usr/bin/env python3
"""
Test script to upload comb_compatibility.py to remote server, execute it, and download results.
"""

import sys
import subprocess
import webbrowser
from pathlib import Path
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

# Server configuration
SERVER_HOST = "*************"
SERVER_USER = "root"
SERVER_PASSWORD = "1234567"
SERVER_TEMP_DIR = "/tmp"
REMOTE_PYTHON = "python3.12"

# Local files
LOCAL_SCRIPT = "comb_compatibility.py"
REMOTE_SCRIPT = f"{SERVER_TEMP_DIR}/comb_compatibility.py"
REMOTE_OUTPUT = f"{SERVER_TEMP_DIR}/flattened.pdf"
LOCAL_OUTPUT = "flattened.pdf"

def check_dependencies():
    """Check if required tools are available"""
    required_tools = ['sshpass', 'ssh', 'scp']
    missing_tools = []
    
    for tool in required_tools:
        try:
            subprocess.run([tool, '--help'], capture_output=True, check=False)
        except FileNotFoundError:
            missing_tools.append(tool)
    
    if missing_tools:
        logger.error(f"Missing required tools: {', '.join(missing_tools)}")
        logger.error("Please install them:")
        if 'sshpass' in missing_tools:
            logger.error("  brew install sshpass  # macOS")
            logger.error("  sudo apt-get install sshpass  # Ubuntu/Debian")
        return False
    
    return True

def run_ssh_command(command, capture_output=True):
    """Run SSH command on remote server"""
    ssh_cmd = [
        'sshpass', '-p', SERVER_PASSWORD,
        'ssh', '-o', 'StrictHostKeyChecking=no',
        f'{SERVER_USER}@{SERVER_HOST}',
        command
    ]
    
    logger.debug(f"Running SSH command: {' '.join(ssh_cmd[:-1])} [command]")
    
    try:
        result = subprocess.run(ssh_cmd, capture_output=capture_output, text=True, check=True)
        return result
    except subprocess.CalledProcessError as e:
        logger.error(f"SSH command failed: {e}")
        if e.stdout:
            logger.error(f"STDOUT: {e.stdout}")
        if e.stderr:
            logger.error(f"STDERR: {e.stderr}")
        raise

def upload_file(local_path, remote_path):
    """Upload file to remote server using SCP"""
    scp_cmd = [
        'sshpass', '-p', SERVER_PASSWORD,
        'scp', '-o', 'StrictHostKeyChecking=no',
        local_path,
        f'{SERVER_USER}@{SERVER_HOST}:{remote_path}'
    ]
    
    logger.info(f"Uploading {local_path} to {SERVER_HOST}:{remote_path}")
    
    try:
        subprocess.run(scp_cmd, check=True)
        logger.info("Upload successful")
    except subprocess.CalledProcessError as e:
        logger.error(f"Upload failed: {e}")
        raise

def download_file(remote_path, local_path):
    """Download file from remote server using SCP"""
    scp_cmd = [
        'sshpass', '-p', SERVER_PASSWORD,
        'scp', '-o', 'StrictHostKeyChecking=no',
        f'{SERVER_USER}@{SERVER_HOST}:{remote_path}',
        local_path
    ]
    
    logger.info(f"Downloading {SERVER_HOST}:{remote_path} to {local_path}")
    
    try:
        subprocess.run(scp_cmd, check=True)
        logger.info("Download successful")
    except subprocess.CalledProcessError as e:
        logger.error(f"Download failed: {e}")
        raise

def create_test_pdf():
    """Create a simple test PDF for testing"""
    test_pdf_path = "out1.pdf"

    if Path(test_pdf_path).exists():
        logger.info(f"Test PDF {test_pdf_path} already exists")
        return test_pdf_path

    logger.info(f"Creating test PDF: {test_pdf_path}")

    # Create a minimal valid PDF file for testing
    with open(test_pdf_path, 'wb') as f:
        # Minimal PDF with a simple form field
        pdf_content = b"""%PDF-1.4
1 0 obj
<< /Type /Catalog /Pages 2 0 R /AcroForm << /Fields [4 0 R] /NeedAppearances true >> >>
endobj
2 0 obj
<< /Type /Pages /Kids [3 0 R] /Count 1 >>
endobj
3 0 obj
<< /Type /Page /Parent 2 0 R /MediaBox [0 0 612 792] /Annots [4 0 R] >>
endobj
4 0 obj
<< /Type /Annot /Subtype /Widget /FT /Tx /T (TestField) /V (Test) /Rect [100 700 300 720] /Ff 16777216 /MaxLen 10 >>
endobj
xref
0 5
0000000000 65535 f
0000000010 00000 n
0000000108 00000 n
0000000165 00000 n
0000000248 00000 n
trailer
<< /Size 5 /Root 1 0 R >>
startxref
380
%%EOF
"""
        f.write(pdf_content)

    logger.info(f"Created test PDF with form field: {test_pdf_path}")
    return test_pdf_path

def main():
    """Main function"""
    logger.info("PDF Remote Processing Test Script")
    logger.info("=" * 50)
    
    # Check if local script exists
    if not Path(LOCAL_SCRIPT).exists():
        logger.error(f"Local script {LOCAL_SCRIPT} not found")
        return 1
    
    # Check dependencies
    if not check_dependencies():
        return 1
    
    try:
        # Create test PDF if needed
        test_pdf = create_test_pdf()
        
        # Step 1: Upload the script to server
        logger.info("Step 1: Uploading script to server")
        upload_file(LOCAL_SCRIPT, REMOTE_SCRIPT)
        
        # Step 2: Upload test PDF to server
        logger.info("Step 2: Uploading test PDF to server")
        remote_input_pdf = f"{SERVER_TEMP_DIR}/out1.pdf"
        upload_file(test_pdf, remote_input_pdf)
        
        # Step 3: Make script executable and check Python version
        logger.info("Step 3: Checking remote environment")
        run_ssh_command(f"chmod +x {REMOTE_SCRIPT}")
        
        # Check if Python 3.12 is available
        global REMOTE_PYTHON
        try:
            result = run_ssh_command(f"{REMOTE_PYTHON} --version")
            logger.info(f"Remote Python version: {result.stdout.strip()}")
        except subprocess.CalledProcessError:
            logger.warning(f"{REMOTE_PYTHON} not found, trying python3")
            REMOTE_PYTHON = "python3"
            result = run_ssh_command(f"{REMOTE_PYTHON} --version")
            logger.info(f"Remote Python version: {result.stdout.strip()}")
        
        # Step 4: Execute the script remotely
        logger.info("Step 4: Executing script on remote server")
        remote_cmd = f"cd {SERVER_TEMP_DIR} && {REMOTE_PYTHON} comb_compatibility.py out1.pdf out2.pdf"
        logger.info(f"Remote command: {remote_cmd}")

        result = run_ssh_command(remote_cmd, capture_output=True)

        if result.stdout:
            logger.info("Remote execution output:")
            for line in result.stdout.strip().split('\n'):
                logger.info(f"  {line}")

        if result.stderr:
            logger.warning("Remote execution warnings/errors:")
            for line in result.stderr.strip().split('\n'):
                logger.warning(f"  {line}")

        # Step 5: Execute Ghostscript command to flatten the PDF
        logger.info("Step 5: Executing Ghostscript command to flatten PDF")
        gs_cmd = f"cd {SERVER_TEMP_DIR} && gs -o flattened.pdf -sDEVICE=pdfwrite out2.pdf"
        logger.info(f"Ghostscript command: {gs_cmd}")

        gs_result = run_ssh_command(gs_cmd, capture_output=True)

        if gs_result.stdout:
            logger.info("Ghostscript execution output:")
            for line in gs_result.stdout.strip().split('\n'):
                logger.info(f"  {line}")

        if gs_result.stderr:
            logger.warning("Ghostscript execution warnings/errors:")
            for line in gs_result.stderr.strip().split('\n'):
                logger.warning(f"  {line}")

        # Step 6: Check if flattened output file was created
        logger.info("Step 6: Checking if flattened output file was created")
        try:
            run_ssh_command(f"ls -la {REMOTE_OUTPUT}")
            logger.info("Flattened output file created successfully on remote server")
        except subprocess.CalledProcessError:
            logger.error("Flattened output file was not created on remote server")
            return 1
        
        # Step 7: Download the result
        logger.info("Step 7: Downloading result file")
        download_file(REMOTE_OUTPUT, LOCAL_OUTPUT)

        # Step 8: Verify local file
        if Path(LOCAL_OUTPUT).exists():
            file_size = Path(LOCAL_OUTPUT).stat().st_size
            logger.info(f"Downloaded file size: {file_size:,} bytes")

            # Step 9: Open in browser
            logger.info("Step 9: Opening PDF in browser")
            file_url = f"file://{Path(LOCAL_OUTPUT).absolute()}"
            webbrowser.open(file_url)
            logger.info(f"Opened {LOCAL_OUTPUT} in default browser")
        else:
            logger.error("Downloaded file not found locally")
            return 1
        
        # Cleanup remote files
        logger.info("Cleaning up remote files")
        try:
            remote_out2_pdf = f"{SERVER_TEMP_DIR}/out2.pdf"
            run_ssh_command(f"rm -f {REMOTE_SCRIPT} {remote_input_pdf} {remote_out2_pdf} {REMOTE_OUTPUT}")
            logger.info("Remote cleanup completed")
        except subprocess.CalledProcessError:
            logger.warning("Remote cleanup failed (files may still exist)")
        
        logger.info("=" * 50)
        logger.info("✓ All steps completed successfully!")
        logger.info(f"✓ Result file: {LOCAL_OUTPUT}")
        
        return 0
        
    except Exception as e:
        logger.error(f"Error during execution: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
